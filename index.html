<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="logo.PNG">
    <title>Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <!-- Google Fonts for Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700&display=swap" rel="stylesheet">
</head>
<body>


    <!-- Header Section -->
    <header>
        <div class="logo">
            <!-- Make the logo clickable and link to the main page -->
            <a href="index.html">
                <img src="logo.PNG" alt="Logo" class="logo-img">
            </a>
        </div>
        <a href="https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93" class="cv-button">Get CV</a>
    </header>

    

    <!-- Intro Section -->
    <section class="intro">
        <h1>Welcome, I'M<br><span class="highlight">Chouchane Med Amine</span></h1>
        <p>Software Developer</p>
    </section>

    <!-- Combined Intro-Image and Crafting Section -->
    <section class="intro-crafting">
        <div class="crafting-content">
            <h2>CRAFTING UNIQUE<br>DIGITAL EXPERIENCES</h2>
            <p>I specialize in delivering creative and functional solutions, combining design expertise with innovative web development to bring ideas to life.</p>
            <div class="services">
                <div class="service-item">
                    <img src="web-development-icon.jpg" alt="Icon" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover; display: block; margin: 0 auto 20px; border: 2px solid #FF2D55;">
                    <h3>WEB DEV</h3>
                    <p>Creating responsive, modern websites tailored to meet client needs and goals.</p>
                </div>
                <div class="service-item">
                    <img src="branding.jpeg" alt="Icon" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover; display: block; margin: 0 auto 20px; border: 2px solid #FF2D55;">
                    <h3>BRANDING</h3>
                    <p>Building strong brand identities that resonate and leave a lasting impression.</p>
                </div>
                <div class="service-item">
                    <img src="UI UX.png" alt="Icon" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover; display: block; margin: 0 auto 20px; border: 2px solid #FF2D55;">
                    <h3>UI/UX DESIGN</h3>
                    <p>Enhancing usability and aesthetics through well-thought-out interface designs.</p>
                </div>
            </div>
        </div>
        <div class="intro-image-wrapper">
            <div class="intro-image">
                <img src="Picsart_Pro.png" alt="Intro Image">
            </div>
            <div class="social-links">
                <a href="https://www.linkedin.com/in/chouchane-amine-932324320/"><img src="icons8-linkedin-circled-64.png" alt="Linkedin"></a>
                <a href="https://www.instagram.com/_ami_nos_"><img src="icons8-instagram-64.png" alt="Instagram"></a>
                <a href="https://github.com/aminos555"><img src="icons8-github-64.png" alt="GitHub"></a>
            </div>
            <div class="skills-bar">
                <span>UI/UX DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE</span>
            </div>
        </div>
    </section>

    <!-- Skills Ticker (Between Intro and Crafting) -->
    <div class="skills-ticker">
        <div class="ticker-track">
            <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
            <!-- Duplicate for infinite scroll -->
            <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
        </div>
    </div>

    <!-- Statistics Section -->
    <section class="statistics">
        <h2>STATISTICS</h2>
        <div class="stats-grid">
            <div class="stat">
                <h3>9+</h3>
                <p>WEBSITES DEVELOPED</p>
            </div>
            <div class="stat">
                <h3>14</h3>
                <p>Web DESIGN PROJECTS</p>
            </div>
            <div class="stat">
                <h3>2+</h3>
                <p>YEARS OF EXPERIENCE</p>
            </div>
            <!-- <div class="stat">
                <h3>70+</h3>
                <p>SATISFIED CLIENTS</p>
            </div> -->
        </div>
        <!-- Placeholder for Statistics Image -->
        <div class="stats-image">
            <img src="business-8398066.jpg" alt="Statistics Image">
        </div>
        <a href="lets-talk.html" class="action-button">LET'S TALK</a>
    </section>

    <!-- Experience Section -->
    <section class="experience">
        <h2>Professional Experience</h2>
        <div class="timeline">
            <!-- Experience Item 1 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <a href="job1-senior-fullstack.html" class="timeline-content-link">
                    <div class="timeline-content">
                        <img src="https://via.placeholder.com/80x80/4B0082/FFFFFF?text=TC" alt="TechCorp Logo" class="company-logo">
                        <h3 class="job-title">Senior Full Stack Developer</h3>
                        <h4 class="company-name">TechCorp Solutions</h4>
                        <p class="job-duration">2023 - Present</p>
                        <p class="job-description">Leading development of scalable web applications using React, Node.js, and modern cloud technologies. Mentoring junior developers and implementing best practices for code quality and performance optimization.</p>
                        <div class="view-details">
                            <span>View Details →</span>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Experience Item 2 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <a href="job2-uiux-frontend.html" class="timeline-content-link">
                    <div class="timeline-content">
                        <img src="https://via.placeholder.com/80x80/FF2D55/FFFFFF?text=DS" alt="DigitalStudio Logo" class="company-logo">
                        <h3 class="job-title">UI/UX Designer & Frontend Developer</h3>
                        <h4 class="company-name">DigitalStudio Creative</h4>
                        <p class="job-duration">2022 - 2023</p>
                        <p class="job-description">Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.</p>
                        <div class="view-details">
                            <span>View Details →</span>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Experience Item 3 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <a href="job3-junior-webdev.html" class="timeline-content-link">
                    <div class="timeline-content">
                        <img src="https://via.placeholder.com/80x80/00CED1/FFFFFF?text=WD" alt="WebDev Agency Logo" class="company-logo">
                        <h3 class="job-title">Junior Web Developer</h3>
                        <h4 class="company-name">WebDev Agency</h4>
                        <p class="job-duration">2021 - 2022</p>
                        <p class="job-description">Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.</p>
                        <div class="view-details">
                            <span>View Details →</span>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Experience Item 4 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <a href="job4-freelance-designer.html" class="timeline-content-link">
                    <div class="timeline-content">
                        <img src="https://via.placeholder.com/80x80/32CD32/FFFFFF?text=FL" alt="Freelance Logo" class="company-logo">
                        <h3 class="job-title">Freelance Designer</h3>
                        <h4 class="company-name">Self-Employed</h4>
                        <p class="job-duration">2020 - 2021</p>
                        <p class="job-description">Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.</p>
                        <div class="view-details">
                            <span>View Details →</span>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="portfolio">
        <h2>Top Projects<br></h2>
        <a href="#" class="discover-button">DISCOVER MORE</a>
        <!-- Carousel for Portfolio Items -->
        <div class="portfolio-carousel">
            <div class="carousel-track">
                <!-- Portfolio Item 0 -->
                <div class="portfolio-item">
                    <a href="https://threed-e-commerce.onrender.com" target="_blank">
                        <img src="3D E-Comm.PNG" alt="3D Ecommerce">
                        <p>3D Ecommerce (Finsh Soon)</p>
                    </a>
                </div>
                <!-- Portfolio Item 1 -->
                <div class="portfolio-item">
                    <a href="project1.html" target="_blank">
                        <img src="ex1.webp" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <!-- Portfolio Item 2 -->
                <div class="portfolio-item">
                    <a href="project2.html" target="_blank">
                        <img src="ex2.png" alt="Nexit Brand Identity">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <!-- Portfolio Item 3 -->
                <div class="portfolio-item">
                    <a href="project3.html" target="_blank">
                        <img src="ex3.webp" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <!-- Portfolio Item 4 -->
                <div class="portfolio-item">
                    <a href="project4.html" target="_blank">
                        <img src="ex4.1.png" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <!-- Portfolio Item 5 -->
                <div class="portfolio-item">
                    <a href="project5.html" target="_blank">
                        <img src="ex5.png" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <!-- Portfolio Item 6 -->
                <div class="portfolio-item">
                    <a href="bussniss web UI" target="_blank">
                        <img src="bussniss web UI.PNG" alt="Yalla Go Posters">
                        <p>Avalabel in git Will be deployed soon.</p>
                    </a>
                </div>

                <div class="portfolio-item">
                    <a href="https://threed-e-commerce.onrender.com" target="_blank">
                        <img src="3D E-Comm.PNG" alt="3D Ecommerce">
                        <p>3D Ecommerce (not responsive yet )</p>
                    </a>
                </div>
                
                <!-- Duplicates for Infinite Scroll -->
                <div class="portfolio-item">
                    <a href="project1.html" target="_blank">
                        <img src="ex1.webp" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <div class="portfolio-item">
                    <a href="project2.html" target="_blank">
                        <img src="ex2.png" alt="Nexit Brand Identity">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <div class="portfolio-item">
                    <a href="project3.html" target="_blank">
                        <img src="ex3.webp" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <div class="portfolio-item">
                    <a href="project4.html" target="_blank">
                        <img src="ex4.1.png" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>
                <div class="portfolio-item">
                    <a href="project5.html" target="_blank">
                        <img src="ex5.png" alt="Yalla Go Posters">
                        <p>Will be deployed soon.</p>
                    </a>
                </div>

                <!-- Portfolio Item 6 -->
                <div class="portfolio-item">
                    <a href="bussniss web UI" target="_blank">
                        <img src="bussniss web UI.PNG" alt="Yalla Go Posters">
                        <p>Avalabel in git Will be deployed soon.</p>
                    </a>
                </div>

            </div>
        </div>
    </section>

    <!-- Skills Ticker (Between Portfolio and Client Thoughts) -->
    <div class="skills-ticker">
        <div class="ticker-track">
            <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
            <!-- Duplicate for infinite scroll -->
            <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
        </div>
    </div>

    <!-- Client Thoughts Section -->
    <section class="client-thoughts">
        <div class="quote-icon">
            <!-- <img src="quote-icon.png" alt="Quote Icon" style="width: 40px; height: auto;"> -->
        </div>
        <h2>CLIENT THOUGHTS</h2>
        <h3>“E-COMMERCE EXPERTISE”</h3>
        <p>Will be Available Soon . . . </p>
        <!-- <p class="client-name">[CLIENT NAME]</p> -->
        <div class="thoughts-image">
            <img src="client touch.jpg" alt="E-commerce Expertise Visual" style="width: 100%; max-width: 600px; height: auto; margin: 20px 0; border: 2px solid #FF2D55; border-radius: 10px;">
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="contact-overlay">
            <h2>LET'S CREATE TOGETHER</h2>
            <form action="https://formspree.io/f/mblgroaz" method="POST">
                <label for="name">YOUR NAME</label>
                <input type="text" id="name" name="name" placeholder="NAME" required>
                <label for="email">YOUR EMAIL</label>
                <input type="email" id="email" name="email" placeholder="EMAIL" required>
                <label for="services">WHAT SERVICES ARE YOU LOOKING FOR?</label>
                <input type="text" id="services" name="services" placeholder="Web Design, Graphic design..." required>
                <label for="message">YOUR MESSAGE</label>
                <textarea id="message" name="message" placeholder="YOUR MESSAGE" required></textarea>
                <label for="linkedin">Your LinkedIn <span style="color: green;">(Optional)</span></label>
                <input type="url" id="linkedin" name="linkedin" placeholder="https://www.linkedin.com/in/your-profile">
                <button type="submit" class="submit-button">SEND</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <p>2025 REECRAFT. ALL RIGHTS RESERVED.</p>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top">↑</a>

    <!-- JavaScript for Enhanced Carousel with Continuous Movement and Wheel Control -->
    <script>
        const carouselTrack = document.querySelector('.carousel-track');
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        let isDragging = false;
        let isHovering = false;
        let startX;
        let startScrollLeft;
        let autoScrollInterval;
        let scrollSpeed = 1; // Base scroll speed

        // Debugging function to log state
        function logState(message) {
            console.log(message, {
                isDragging,
                isHovering,
                scrollLeft: carouselTrack ? carouselTrack.scrollLeft : 'N/A',
                scrollWidth: carouselTrack ? carouselTrack.scrollWidth : 'N/A',
                clientWidth: carouselTrack ? carouselTrack.clientWidth : 'N/A'
            });
        }

        // Enhanced auto-scroll with continuous movement
        function startAutoScroll() {
            stopAutoScroll(); // Clear any existing interval
            if (carouselTrack) {
                autoScrollInterval = setInterval(() => {
                    if (!isDragging && !isHovering) {
                        carouselTrack.scrollLeft += scrollSpeed;
                        const trackWidth = carouselTrack.scrollWidth / 2; // Half width for one cycle
                        if (carouselTrack.scrollLeft >= trackWidth) {
                            carouselTrack.scrollLeft = 0; // Seamless loop back to start
                        }
                        // Optional: Remove debug logging for smoother performance
                        // logState('Auto-scrolling');
                    }
                }, 16); // ~60fps for smoother animation
            }
        }

        // Stop auto-scroll
        function stopAutoScroll() {
            if (autoScrollInterval) {
                clearInterval(autoScrollInterval);
                autoScrollInterval = null;
                logState('Auto-scroll stopped');
            }
        }

        // Start auto-scroll on page load
        window.addEventListener('load', () => {
            if (carouselTrack) {
                startAutoScroll();
                logState('Initialized');
            } else {
                console.error('carouselTrack not found!');
            }
        });

        // Enhanced hover controls - pause on carousel hover
        carouselTrack.addEventListener('mouseenter', () => {
            if (carouselTrack) {
                isHovering = true;
                logState('Paused on carousel hover');
            }
        });

        carouselTrack.addEventListener('mouseleave', () => {
            if (carouselTrack) {
                isHovering = false;
                if (!isDragging) {
                    logState('Resumed on carousel leave');
                }
            }
        });

        // Mouse wheel control for carousel
        carouselTrack.addEventListener('wheel', (e) => {
            if (carouselTrack) {
                e.preventDefault();
                const wheelDelta = e.deltaY;
                const scrollAmount = wheelDelta > 0 ? 50 : -50; // Scroll amount per wheel step

                carouselTrack.scrollLeft += scrollAmount;

                // Handle infinite loop for wheel scrolling
                const trackWidth = carouselTrack.scrollWidth / 2;
                if (carouselTrack.scrollLeft >= trackWidth) {
                    carouselTrack.scrollLeft = 0;
                } else if (carouselTrack.scrollLeft < 0) {
                    carouselTrack.scrollLeft = trackWidth - 1;
                }

                logState('Wheel scrolling');
            }
        }, { passive: false });

        // Enhanced Mouse Drag Events
        carouselTrack.addEventListener('mousedown', (e) => {
            if (!carouselTrack) return;
            isDragging = true;
            isHovering = true; // Ensure auto-scroll stays paused during drag
            carouselTrack.classList.add('dragging');
            startX = e.pageX;
            startScrollLeft = carouselTrack.scrollLeft;
            logState('Mouse drag started');
        });

        carouselTrack.addEventListener('mousemove', (e) => {
            if (!isDragging || !carouselTrack) return;
            const x = e.pageX;
            const walk = (x - startX) * 1.8; // Slightly increased drag sensitivity
            carouselTrack.scrollLeft = startScrollLeft - walk;

            // Handle infinite loop for dragging
            const trackWidth = carouselTrack.scrollWidth / 2;
            if (carouselTrack.scrollLeft >= trackWidth) {
                carouselTrack.scrollLeft = 0;
            } else if (carouselTrack.scrollLeft < 0) {
                carouselTrack.scrollLeft = trackWidth - 1;
            }
            // Reduced logging for better performance
            // logState('Mouse dragging');
        });

        document.addEventListener('mouseup', () => {
            if (isDragging && carouselTrack) {
                isDragging = false;
                carouselTrack.classList.remove('dragging');
                // Check if mouse is still over carousel before resuming auto-scroll
                setTimeout(() => {
                    if (!isHovering) {
                        // Auto-scroll will resume automatically since isHovering is false
                    }
                }, 100);
                logState('Mouse drag ended');
            }
        });

        // Enhanced Touch Drag Events
        carouselTrack.addEventListener('touchstart', (e) => {
            if (!carouselTrack) return;
            isDragging = true;
            isHovering = true;
            carouselTrack.classList.add('dragging');
            startX = e.touches[0].pageX;
            startScrollLeft = carouselTrack.scrollLeft;
            logState('Touch drag started');
        }, { passive: false });

        carouselTrack.addEventListener('touchmove', (e) => {
            if (!isDragging || !carouselTrack) return;
            e.preventDefault();
            const x = e.touches[0].pageX;
            const walk = (x - startX) * 1.8;
            carouselTrack.scrollLeft = startScrollLeft - walk;

            const trackWidth = carouselTrack.scrollWidth / 2;
            if (carouselTrack.scrollLeft >= trackWidth) {
                carouselTrack.scrollLeft = 0;
            } else if (carouselTrack.scrollLeft < 0) {
                carouselTrack.scrollLeft = trackWidth - 1;
            }
            // Reduced logging for performance
            // logState('Touch dragging');
        }, { passive: false });

        carouselTrack.addEventListener('touchend', () => {
            if (isDragging && carouselTrack) {
                isDragging = false;
                isHovering = false;
                carouselTrack.classList.remove('dragging');
                logState('Touch drag ended');
            }
        }, { passive: false });

        // Individual portfolio item hover effects
        portfolioItems.forEach((item, index) => {
            item.addEventListener('mouseenter', () => {
                logState(`Portfolio item ${index} hovered`);
                // Additional hover effects can be added here if needed
            });

            item.addEventListener('mouseleave', () => {
                logState(`Portfolio item ${index} unhovered`);
            });
        });

        // Enhanced visibility change handler
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is hidden, pause auto-scroll to save resources
                logState('Page hidden - pausing auto-scroll');
            } else if (carouselTrack && !isDragging && !isHovering) {
                // Page is visible and no user interaction - resume auto-scroll
                logState('Page visible - resuming auto-scroll');
            }
        });

        // Performance optimization: Reduce console logging in production
        // Uncomment the line below to disable debug logging for better performance
        // logState = () => {};

        console.log('Enhanced carousel initialized with continuous movement, hover controls, and wheel support!');
        </script>
</body>
</html>